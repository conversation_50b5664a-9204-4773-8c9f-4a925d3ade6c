package service

import (
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"gorm.io/gorm"
	"strconv"
	"time"
)

// TaskService 任务服务层接口
type TaskService interface {
	// CreateTask 创建任务
	CreateTask(reportID string) (uint, string, error)

	// GetTaskByID 根据ID获取任务详情
	GetTaskByID(id uint) (*model.Task, error)

	// ListTasks 分页查询任务列表（支持多条件筛选）
	ListTasks(page, pageSize int, userID uint, status model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(id uint, status model.TaskStatus) error

	// QueryReport 查询报告
	QueryReport(id uint) (*types.ReportInfo, error)
}

// CreateTaskResult 创建任务结果
type CreateTaskResult struct {
	UserID   uint   `json:"user_id"`
	Password string `json:"password"`
	Code     string `json:"code"`
	Msg      string `json:"msg"`
}

// taskService 任务服务层实现
type taskService struct {
	taskDao dao.TaskDao
	userS   UserService
	riskS   RiskService
}

// NewTaskService 创建任务服务层实例
func NewTaskService(taskDao dao.TaskDao, userS UserService, riskS RiskService) TaskService {
	return &taskService{
		taskDao: taskDao,
		userS:   userS,
		riskS:   riskS,
	}
}

// CreateTask 创建任务
func (s *taskService) CreateTask(reportID string) (uint, string, error) {
	// 校验reportID是否已被创建
	existingTask, err := s.taskDao.GetByReportID(reportID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, "", err
	}
	if existingTask != nil {
		return 0, "", utils.ErrTaskReportIDExist
	}

	// TODO 查询报告
	reportInfo, err := s.QueryReport(1)
	if err != nil {
		return 0, "", err
	}

	userID, password, err := s.userS.InitByUsername(reportInfo.CompanyName, reportInfo.CreditCode)
	if err != nil {
		return 0, "", err
	}

	// 创建任务
	task := &model.Task{
		UserID:     userID,
		Status:     model.TaskStatusPending,
		ReportID:   reportID,
		TestPeriod: reportInfo.TestPeriod,
	}

	err = s.taskDao.Create(task)
	if err != nil {
		return 0, "", utils.NewAppError(utils.ErrInternalCode, "创建任务失败")
	}

	riskList := make([]*model.Risk, 0, len(reportInfo.RiskList))
	for _, risk := range reportInfo.RiskList {
		perRisk := &model.Risk{
			TaskID:             task.ID,
			RiskLevel:          risk.RiskLevel,
			IndicatorType:      risk.IndicatorType,
			IndicatorName:      risk.IndicatorName,
			RiskDetail:         risk.RiskDetail,
			RiskType:           risk.RiskType,
			ResponseSuggestion: risk.ResponseSuggestion,
			LegalBasis:         risk.LegalBasis,
		}
		riskList = append(riskList, perRisk)
	}

	err = s.riskS.BatchCreateRisk(riskList)
	if err != nil {
		return 0, "", err
	}

	return userID, password, nil
}

// GetTaskByID 根据ID获取任务详情
func (s *taskService) GetTaskByID(id uint) (*model.Task, error) {
	if id == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "请填写任务ID")
	}
	task, err := s.taskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return nil, err
	}
	return task, nil
}

// ListTasks 分页查询任务列表（支持多条件筛选）
func (s *taskService) ListTasks(page, pageSize int, userID uint, status model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	list, i, err := s.taskDao.List(page, pageSize, userID, status, startTime, endTime)
	if err != nil {
		return nil, 0, err
	}

	return list, i, nil
}

// UpdateTaskStatus 更新任务状态
func (s *taskService) UpdateTaskStatus(id uint, status model.TaskStatus) error {
	task, err := s.taskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return err
	}

	// 如果状态相同，直接返回
	if task.Status == status {
		return nil
	}

	// 验证状态流转是否合法
	if !task.CanTransitionTo(status) {
		return utils.NewAppError(utils.ErrTaskStatusTransitionCode, "任务状态错误")
	}

	err = s.taskDao.Update(id, status)
	if err != nil {
		return err
	}

	return nil
}

// QueryReport 查询报告
func (s *taskService) QueryReport(id uint) (*types.ReportInfo, error) {
	if id == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "请填写报告ID")
	}

	// 构建查询URL
	path := "http://example.com/api/report"

	// 设置请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	idStr := strconv.Itoa(int(id))
	queryParams := map[string]string{
		"id": idStr,
	}
	// 调用utils.Post函数发送请求
	response, err := utils.Post(path, nil, headers, queryParams)
	if err != nil {
		return nil, utils.NewAppError(utils.ErrInternalCode, "查询报告失败")
	}
	_ = response

	// TODO 查询或下载数据
	// TODO 测试数据
	reportInfo := &types.ReportInfo{
		ReportID:       "123456",
		CreditCode:     "91510124MACXXQLK78",
		TestPeriod:     "2023年10月-2025年07月",
		CompanyName:    "蓉创政通财税咨询（成都）有限公司",
		AccountingType: "独立核算自负盈亏",
		RiskList: []types.RiskInfo{
			{
				RiskLevel:          model.RiskLevelMedium,
				IndicatorType:      "增值税",
				IndicatorName:      "应收款增加额与销售收入比异常",
				RiskDetail:         "说明公司可能存在：公司利用赊销政策、代销政策，已实际销售货物，但未按纳税义务发生时间及时确认收入或者虚假申报的风险。",
				RiskType:           model.RiskTypeNormal,
				ResponseSuggestion: "请核实库存商品、其他应收账款明细账、记账凭证、货物交割单、合同协议等资料，是否存在未及时确认收入的情况。",
				LegalBasis:         "《中华人民共和国增值税暂行条例》第一条、第十九条，《中华人民共和国增值税暂行条例实施细则》第四条、第十六条、第三十三条，《中华人民共和国税收征收管理法》第三十二条、第六十三条第一款。",
			},
			{
				RiskLevel:          model.RiskLevelMedium,
				IndicatorType:      "提示提醒",
				IndicatorName:      "缺少水电房租发票",
				RiskDetail:         "公司在营业期间，取得较少水电通讯房租物业等费用，与正常企业生产经营特征不符合。若公司仍处于新成立开办期，或生产经营地为股东所有，则此指标风险较小。若公司注册地为园区、法人为高风险人群、顶额开票、收入阶段性暴增等特征，可能存在空壳企业虚开发票的风险。",
				RiskType:           model.RiskTypeSpecific,
				ResponseSuggestion: "请核实公司水电通讯房租物业等生产经营费用发票是否足额取得。",
				LegalBasis:         "《中华人民共和国发票管理办法》第二十一条、第三十五条、《中华人民共和国企业所得税法》第八条，《国家税务总局关于发布《企业所得税税前扣除凭证管理办法》的公告》（国家税务总局公告2018年第28号）第二条、第四条。",
			},
		},
	}

	return reportInfo, nil
}
