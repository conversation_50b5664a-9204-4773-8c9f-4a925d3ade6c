package model

import "gorm.io/gorm"

// RiskLevel 风险类别枚举
type RiskLevel string

const (
	// RiskLevelHigh 高风险
	RiskLevelHigh RiskLevel = "high"
	// RiskLevelMedium 中风险
	RiskLevelMedium RiskLevel = "medium"
	// RiskLevelLow 低风险
	RiskLevelLow RiskLevel = "low"
)

// RiskType 风险类型
type RiskType string

const (
	// RiskTypeSpecific 特定
	RiskTypeSpecific RiskType = "specific"
	// RiskTypeNormal 普通
	RiskTypeNormal RiskType = "normal"
)

// Risk 风险表模型
type Risk struct {
	gorm.Model
	TaskID             uint      `gorm:"column:task_id;type:int unsigned;not null;index" json:"task_id"`               // 任务ID
	RiskLevel          RiskLevel `gorm:"column:risk_level;type:varchar(20);not null;index" json:"risk_level"`          // 风险类别（高、中、低风险）
	IndicatorType      string    `gorm:"column:indicator_type;type:varchar(100);not null;index" json:"indicator_type"` // 指标大类（如增值税、提示提醒等）
	IndicatorName      string    `gorm:"column:indicator_name;type:varchar(200);not null" json:"indicator_name"`       // 指标名称
	RiskDetail         string    `gorm:"column:risk_detail;type:text" json:"risk_detail"`                              // 风险详情
	RiskType           RiskType  `gorm:"column:risk_type;type:varchar(20);not null;index" json:"risk_type"`
	ResponseSuggestion string    `gorm:"column:response_suggestion;type:text" json:"response_suggestion"` // 应对建议
	LegalBasis         string    `gorm:"column:legal_basis;type:text" json:"legal_basis"`                 // 法律依据
}
